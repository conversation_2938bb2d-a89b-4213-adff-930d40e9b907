@echo off
echo Running Computer Graphics Program...
echo.

REM Check if executable exists
if not exist line_program.exe (
    echo Executable not found. Compiling first...
    call compile.bat
    echo.
)

REM Run the program if it exists
if exist line_program.exe (
    echo Starting line drawing program...
    echo.
    line_program.exe
) else (
    echo Failed to create executable. Please check compilation errors.
    pause
)
