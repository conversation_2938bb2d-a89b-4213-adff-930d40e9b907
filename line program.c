#include "graphics lib/winbgim.h"
#include <stdio.h>
#include <stdlib.h>
#include <math.h>

void drawBresenhamLine(int x1, int y1, int x2, int y2) {
    int dx = abs(x2 - x1);
    int dy = abs(y2 - y1);
    int x = x1, y = y1;
    int x_inc = (x1 < x2) ? 1 : -1;
    int y_inc = (y1 < y2) ? 1 : -1;
    int error = dx - dy;

    while (1) {
        putpixel(x, y, WHITE);

        if (x == x2 && y == y2) break;

        int error2 = 2 * error;

        if (error2 > -dy) {
            error -= dy;
            x += x_inc;
        }

        if (error2 < dx) {
            error += dx;
            y += y_inc;
        }
    }
}

int main() {
    int x1, y1, x2, y2;
    int gd = DETECT, gm;

    // Initialize graphics with proper error checking
    initgraph(&gd, &gm, "");

    // Check if graphics initialization was successful
    int errorcode = graphresult();
    if (errorcode != grOk) {
        printf("Graphics error: %s\n", grapherrormsg(errorcode));
        printf("Press any key to halt:");
        getch();
        exit(1);
    }

    // Set background color to black
    setbkcolor(BLACK);
    cleardevice();

    // Display instructions
    outtextxy(10, 10, "Bresenham's Line Drawing Algorithm");
    outtextxy(10, 30, "Enter coordinates in console window");

    // Input coordinates
    printf("Enter the start point coordinates (x1 y1): ");
    scanf("%d %d", &x1, &y1);
    printf("Enter the end point coordinates (x2 y2): ");
    scanf("%d %d", &x2, &y2);

    // Clear screen and redraw
    cleardevice();

    // Draw coordinate axes for reference
    setcolor(DARKGRAY);
    line(0, getmaxy()/2, getmaxx(), getmaxy()/2); // X-axis
    line(getmaxx()/2, 0, getmaxx()/2, getmaxy()); // Y-axis

    // Draw the line using Bresenham's algorithm
    setcolor(WHITE);
    drawBresenhamLine(x1, y1, x2, y2);

    // Mark start and end points
    setcolor(RED);
    circle(x1, y1, 3);
    setcolor(GREEN);
    circle(x2, y2, 3);

    // Display coordinates
    char coord_text[100];
    setcolor(YELLOW);
    sprintf(coord_text, "Start: (%d, %d)", x1, y1);
    outtextxy(10, 10, coord_text);
    sprintf(coord_text, "End: (%d, %d)", x2, y2);
    outtextxy(10, 30, coord_text);
    outtextxy(10, 50, "Press any key to exit...");

    getch();        // Wait for key press
    closegraph();   // Close graphics window
    return 0;
}
