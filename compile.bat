@echo off
echo Compiling Computer Graphics Program...
echo.

REM Compile the line drawing program
gcc -Wall -Wextra -I"graphics lib" -o line_program.exe "line program.c" -L"graphics lib" -lbgi -lgdi32 -lcomdlg32 -luuid -loleaut32 -lole32

if %errorlevel% equ 0 (
    echo.
    echo ✓ Compilation successful!
    echo ✓ Executable created: line_program.exe
    echo.
    echo To run the program, type: line_program.exe
    echo Or double-click the executable file.
    echo.
    pause
) else (
    echo.
    echo ✗ Compilation failed!
    echo Please check for errors above.
    echo.
    pause
)
