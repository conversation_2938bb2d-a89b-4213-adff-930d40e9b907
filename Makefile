# Makefile for Computer Graphics Programs
# Compiler settings
CC = gcc
CFLAGS = -Wall -Wextra -std=c99
LIBS = -lbgi -lgdi32 -lcomdlg32 -luuid -loleaut32 -lole32

# Include paths
INCLUDES = -I"graphics lib"

# Library paths  
LIBPATHS = -L"graphics lib"

# Target executable
TARGET = line_program
SOURCE = line\ program.c

# Default target
all: $(TARGET)

# Build the main program
$(TARGET): $(SOURCE)
	$(CC) $(CFLAGS) $(INCLUDES) -o $(TARGET).exe $(SOURCE) $(LIBPATHS) $(LIBS)

# Clean build files
clean:
	del /f $(TARGET).exe 2>nul || true

# Run the program
run: $(TARGET)
	./$(TARGET).exe

# Help target
help:
	@echo Available targets:
	@echo   all     - Build the program
	@echo   clean   - Remove executable files
	@echo   run     - Build and run the program
	@echo   help    - Show this help message

.PHONY: all clean run help
